<script lang="ts">
  import SEO from '$components/shared/SEO.svelte';
  import PricingCard from '$components/ui/PricingCard.svelte';
  import { Switch } from '$lib/components/ui/switch';
  import { writable } from 'svelte/store';
  import * as Tabs from '$lib/components/ui/tabs';

  import * as Dialog from '$lib/components/ui/dialog'; // Import Dialog component
  import SignIn from '$components/ui/SignIn.svelte';
  import * as ScrollArea from '$lib/components/ui/scroll-area/';
  export let data;

  let selectedPlanId: string | null = data.preselectedPlanId || null;

  // Get section from URL parameter or server data
  const urlParams =
    typeof window !== 'undefined' ? new URLSearchParams(window.location.search) : null;
  const sectionParam = urlParams?.get('section');
  let activeTab =
    sectionParam === 'teams'
      ? 'teams'
      : data.preselectedSection === 'teams'
        ? 'teams'
        : 'individual';

  let billingCycleStore = writable<'monthly' | 'annual'>(
    (data.preselectedBillingCycle === 'annual' ? 'annual' : 'monthly') as 'monthly' | 'annual'
  );
  $: billingCycle = $billingCycleStore;
  $: isAnnual = $billingCycleStore === 'annual';
  let pendingCheckout: {
    planId: string;
    billingCycle: 'monthly' | 'annual';
  } | null = null;
  const user = data.user;
  let plans = data.plans;

  // If plans is undefined or empty, create fallback plans
  if (!plans || plans.length === 0) {
    plans = [
      {
        id: 'free',
        name: 'Free',
        description: 'Basic features for personal use',
        section: 'individual',
        monthlyPrice: 0,
        annualPrice: 0,
        popular: false,
        features: [],
        limits: {},
      },
      {
        id: 'pro',
        name: 'Pro',
        description: 'Advanced features for active job seekers',
        section: 'individual',
        monthlyPrice: 1999,
        annualPrice: 19990,
        popular: true,
        features: [],
        limits: {},
      },
    ];
  }

  // Debug plans after fallback
  console.log('🔍 CLIENT: Final plans:', plans);
  console.log(
    '🔍 CLIENT: Individual plans:',
    plans.filter((p: any) => p.section === 'individual')
  );
  console.log(
    '🔍 CLIENT: Team plans:',
    plans.filter((p: any) => p.section === 'teams')
  );

  // Reactive debug for active tab and filtered plans
  $: {
    console.log('🔍 CLIENT: Active tab:', activeTab);
    console.log('🔍 CLIENT: Billing cycle:', billingCycle);
    console.log('🔍 CLIENT: Is annual:', isAnnual);
    const filteredPlans = plans.filter((p: any) => p.section === activeTab);
    console.log(`🔍 CLIENT: Filtered plans for ${activeTab}:`, filteredPlans);
  }
  let isDialogOpen = false; // This will control the visibility of the dialog
  let isLoading = false; // Loading state for the login process

  // Update billing cycle when isAnnual changes
  $: {
    const newCycle = isAnnual ? 'annual' : 'monthly';
    billingCycleStore.set(newCycle);
    console.log('🔄 CLIENT: Billing cycle updated to:', newCycle);
  }

  // Auto-scroll to the preselected plan
  $: if (selectedPlanId) {
    setTimeout(() => {
      const planElement = document.getElementById(`plan-${selectedPlanId}`);
      if (planElement) {
        planElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }, 500);
  }

  function formatPrice(cents: number) {
    return `${(cents / 100).toFixed(0)}`;
  }

  function calculateSavings(monthlyPrice: number, annualPrice: number) {
    if (monthlyPrice === 0 || annualPrice === 0) return 0;
    const annualMonthlyEquivalent = annualPrice / 12;
    const savings = ((monthlyPrice - annualMonthlyEquivalent) / monthlyPrice) * 100;
    return Math.round(savings);
  }

  async function openStripeCheckout(planId: string, billingCycle: 'monthly' | 'annual') {
    isLoading = true;
    try {
      console.log('Creating checkout session', { planId, billingCycle });

      const res = await fetch('/api/billing/create-checkout-session', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ planId, billingCycle }),
      });

      if (!res.ok) {
        const errorText = await res.text();
        console.error('Failed to create Stripe session', {
          status: res.status,
          statusText: res.statusText,
          errorText,
        });
        alert(`Error: ${errorText || 'Failed to create checkout session'}`);
        isLoading = false;
        return;
      }

      const data = await res.json();

      if (!data.url) {
        console.error('No URL returned from checkout session', data);
        alert('Error: No checkout URL returned');
        isLoading = false;
        return;
      }

      console.log('Redirecting to Stripe', { url: data.url });
      window.location.href = data.url;
    } catch (error) {
      console.error('Failed to create Stripe session', error);
      alert(`Error: ${error.message || 'Failed to create checkout session'}`);
      isLoading = false;
    }
  }

  async function loginWithEmail(email: string, password: string) {
    const res = await fetch('/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, password }),
    });

    if (res.ok) {
      isLoading = false;

      // ✅ If there was a pending checkout, redirect to Stripe
      if (pendingCheckout) {
        const { planId, billingCycle } = pendingCheckout;
        pendingCheckout = null; // clear it
        selectedPlanId = planId;
        openStripeCheckout(planId, billingCycle);
      } else {
        // fallback redirect if needed
        window.location.href = '/dashboard';
      }
    }
  }

  // Function to open the dialog
  function openLoginDialog() {
    isDialogOpen = true;
  }

  // Function to close the dialog
  function closeLoginDialog() {
    isDialogOpen = false;
  }
</script>

<SEO
  title="Pricing | Hirli"
  description="Simple, transparent pricing plans for all your job application automation needs. Choose the plan that fits your career goals."
  keywords="pricing, subscription plans, job application tools, career services, job search automation, resume optimization" />

<div class="container mx-auto">
  <div class="pb-7 pt-14 text-center">
    <h2 class="mb-4 text-3xl font-semibold">Simple, Transparent Pricing</h2>
    <p class="text-muted-foreground font-lg mx-auto mt-2 max-w-2xl">
      Start with a free account to speed up your job hunt or boost your entire team to scale hiring
      process with resume automation.
    </p>
  </div>
</div>
<!-- Tabs for Individual vs Teams -->
<Tabs.Root value={activeTab} onValueChange={(value) => (activeTab = value)} class="w-full">
  <div class="flex-end space-between container mx-auto flex w-full justify-between pb-7">
    <Tabs.List class="rounded-4xl w-fit gap-0 overflow-hidden border-l border-r !p-0">
      <Tabs.Trigger value="individual" class="!m-0 h-7 rounded-none border-none px-4 text-xs"
        >Individual</Tabs.Trigger>
      <Tabs.Trigger value="teams" class="!m-0 h-7 rounded-none border-none px-4 text-xs"
        >Teams</Tabs.Trigger>
    </Tabs.List>

    <div class="flex flex-col items-center justify-between gap-4 px-4 md:flex-row">
      <div class="flex items-center gap-3 text-sm">
        <Switch bind:checked={isAnnual} />
        <span class="text-muted-foreground">Annual</span>
        {#if isAnnual}
          <span class="rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800">
            Save 20%
          </span>
        {/if}
      </div>
    </div>
  </div>

  <Tabs.Content value="individual" class="w-full">
    <!-- Section Description -->
    <div class="mx-auto mb-6 flex flex-col gap-2 text-center">
      <h3 class="text-foreground text-lg font-semibold">Individual Plans</h3>
      <p class="text-muted-foreground mx-auto max-w-xl text-sm">
        Perfect for individual job seekers looking to optimize their applications and land their
        dream job faster.
      </p>
    </div>

    <!-- Plan Cards -->
    <ScrollArea.Root orientation="horizontal" class="mx-10 pb-8">
      <div class="flex flex-row flex-nowrap gap-4">
        {#each plans
          .filter((p: any) => p.section === 'individual')
          .sort((a: any, b: any) => {
            // Always show free plan first in the individual section
            if (a.id === 'free') return -1;
            if (b.id === 'free') return 1;
            // Sort by price
            return a.monthlyPrice - b.monthlyPrice;
          }) as plan (plan.id)}
          <div
            id="plan-{plan.id}"
            class="relative w-[350px] pt-4 {selectedPlanId === plan.id
              ? 'ring-primary ring-2 ring-offset-2'
              : ''}">
            <PricingCard
              title={plan.name}
              price={formatPrice(
                billingCycle === 'monthly' ? plan.monthlyPrice : plan.annualPrice / 12
              )}
              description={plan.description}
              limits={plan.limits}
              features={plan.features}
              {billingCycle}
              isPopular={plan.id === 'pro' ||
                plan.id === 'startup' ||
                plan.popular ||
                selectedPlanId === plan.id}
              activePlan={user && user.role === plan.id}
              loading={selectedPlanId === plan.id && isLoading}
              ctaText={user
                ? user.role === plan.id
                  ? 'Current Plan'
                  : 'Upgrade'
                : plan.id === 'free'
                  ? 'Start Free'
                  : 'Choose Plan'}
              disabled={isLoading || (user && user.role === plan.id)}
              savings={billingCycle === 'annual' && plan.monthlyPrice > 0 && plan.annualPrice > 0
                ? calculateSavings(plan.monthlyPrice, plan.annualPrice)
                : 0}
              onCtaClick={() => {
                if (plan.id === 'free') {
                  // For free plan, just redirect to sign up
                  window.location.href = '/auth/sign-up';
                  return;
                }

                if (!user) {
                  // Store the clicked plan + cycle to trigger after login
                  pendingCheckout = { planId: plan.id, billingCycle };
                  openLoginDialog();
                  return;
                }
                selectedPlanId = plan.id;
                openStripeCheckout(plan.id, billingCycle);
              }} />
          </div>
        {/each}
      </div>
    </ScrollArea.Root>
  </Tabs.Content>

  <Tabs.Content value="teams" class="w-full">
    <!-- Section Description -->
    <div class="mx-auto mb-6 flex flex-col gap-2 text-center">
      <h3 class="text-foreground text-lg font-semibold">Team Plans</h3>
      <p class="text-muted-foreground mx-auto max-w-xl text-sm">
        Collaborate with your team, share resources, and scale your job search efforts with advanced
        team features and bulk operations.
      </p>
    </div>

    <!-- Plan Cards -->
    <ScrollArea.Root orientation="horizontal" class="mx-10 pb-8">
      <div class="flex flex-row flex-nowrap gap-4">
        {#each plans
          .filter((p: any) => p.section === 'teams')
          .sort((a: any, b: any) => {
            // Sort by price
            return a.monthlyPrice - b.monthlyPrice;
          }) as plan (plan.id)}
          <div
            id="plan-{plan.id}"
            class="relative w-[325px] pt-4 {selectedPlanId === plan.id
              ? 'ring-primary ring-2 ring-offset-2'
              : ''}">
            <PricingCard
              title={plan.name}
              price={formatPrice(
                billingCycle === 'monthly' ? plan.monthlyPrice : plan.annualPrice / 12
              )}
              description={plan.description}
              limits={plan.limits}
              features={plan.features}
              {billingCycle}
              isPopular={plan.id === 'pro' ||
                plan.id === 'startup' ||
                plan.popular ||
                selectedPlanId === plan.id}
              activePlan={user && user.role === plan.id}
              loading={selectedPlanId === plan.id && isLoading}
              ctaText={user
                ? user.role === plan.id
                  ? 'Current Plan'
                  : 'Upgrade'
                : plan.id === 'free'
                  ? 'Start Free'
                  : 'Choose Plan'}
              disabled={isLoading || (user && user.role === plan.id)}
              savings={billingCycle === 'annual' && plan.monthlyPrice > 0 && plan.annualPrice > 0
                ? calculateSavings(plan.monthlyPrice, plan.annualPrice)
                : 0}
              onCtaClick={() => {
                if (plan.id === 'free') {
                  // For free plan, just redirect to sign up
                  window.location.href = '/auth/sign-up';
                  return;
                }

                if (!user) {
                  // Store the clicked plan + cycle to trigger after login
                  pendingCheckout = { planId: plan.id, billingCycle };
                  openLoginDialog();
                  return;
                }
                selectedPlanId = plan.id;
                openStripeCheckout(plan.id, billingCycle);
              }} />
          </div>
        {/each}
      </div>
    </ScrollArea.Root>
  </Tabs.Content>
</Tabs.Root>
<!-- CTA -->
<div
  class="border-border bg-card text-card-foreground mt-16 rounded-lg border p-6 text-center shadow-sm">
  <h3 class="mb-2 text-xl font-semibold">Need a custom solution?</h3>
  <p class="text-muted-foreground mb-4">
    Get in touch for enterprise or partner solutions tailored to your needs.
  </p>
  <a href="/contact" class="text-primary font-medium hover:underline">Talk to Sales →</a>
</div>

<!-- Dialog (Login Modal) -->
<Dialog.Root open={isDialogOpen} onOpenChange={closeLoginDialog}>
  <Dialog.Trigger></Dialog.Trigger>
  <!-- This can be removed as it's only needed for triggering -->
  <Dialog.Portal>
    <Dialog.Overlay />
    <Dialog.Content class={`md:w-[375px]`}>
      <Dialog.Header>
        <Dialog.Title class="mb-2 text-2xl">Sign In</Dialog.Title>
        <Dialog.Description>
          <p class="text-muted-foreground text-md mb-6">
            You need to sign in to update your account plan.
          </p>
          <SignIn {isLoading} onEmailPasswordLogin={loginWithEmail} />
        </Dialog.Description>
      </Dialog.Header>
      <Dialog.Footer></Dialog.Footer>
    </Dialog.Content>
  </Dialog.Portal>
</Dialog.Root>
