// Quick script to check how many companies have Clearbit logos
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function checkClearbitLogos() {
  try {
    console.log("🔍 Checking companies with Clearbit logos...");

    // Count companies with Clearbit logos
    const clearbitCount = await prisma.company.count({
      where: {
        logoUrl: {
          contains: "clearbit.com"
        }
      }
    });

    console.log(`📊 Found ${clearbitCount} companies with Clearbit logos`);

    if (clearbitCount > 0) {
      // Get some examples
      const examples = await prisma.company.findMany({
        where: {
          logoUrl: {
            contains: "clearbit.com"
          }
        },
        select: {
          name: true,
          domain: true,
          logoUrl: true,
          activeJobCount: true
        },
        take: 5,
        orderBy: {
          activeJobCount: "desc"
        }
      });

      console.log("\n📋 Examples:");
      examples.forEach((company, index) => {
        console.log(`${index + 1}. ${company.name}`);
        console.log(`   Domain: ${company.domain}`);
        console.log(`   Logo: ${company.logoUrl}`);
        console.log(`   Jobs: ${company.activeJobCount || 0}`);
        console.log("");
      });
    }

  } catch (error) {
    console.error("❌ Error:", error);
  } finally {
    await prisma.$disconnect();
  }
}

checkClearbitLogos();
