// Summary of Clearbit logo migration results
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function migrationSummary() {
  try {
    console.log("📊 Clearbit Logo Migration Summary");
    console.log("=====================================");

    // Count remaining Clearbit logos
    const remainingClearbit = await prisma.company.count({
      where: {
        logoUrl: {
          contains: "clearbit.com"
        }
      }
    });

    // Count companies with R2 logos
    const r2Logos = await prisma.company.count({
      where: {
        logoUrl: {
          contains: "hirli-static-assets.christopher-eugene-rodriguez.workers.dev"
        }
      }
    });

    // Count companies with no logos
    const noLogos = await prisma.company.count({
      where: {
        logoUrl: null
      }
    });

    // Total companies
    const totalCompanies = await prisma.company.count();

    console.log(`📈 Migration Results:`);
    console.log(`   ✅ Companies with R2 logos: ${r2Logos}`);
    console.log(`   ⚠️  Companies still with Clearbit URLs: ${remainingClearbit}`);
    console.log(`   ❌ Companies with no logos: ${noLogos}`);
    console.log(`   📊 Total companies: ${totalCompanies}`);
    console.log("");

    const migrationSuccess = ((r2Logos / (r2Logos + remainingClearbit)) * 100).toFixed(1);
    console.log(`🎯 Migration Success Rate: ${migrationSuccess}%`);
    console.log(`   (${r2Logos} migrated out of ${r2Logos + remainingClearbit} total with logos)`);

    if (remainingClearbit > 0) {
      console.log("\n⚠️  Remaining Clearbit URLs (failed to migrate):");
      const remaining = await prisma.company.findMany({
        where: {
          logoUrl: {
            contains: "clearbit.com"
          }
        },
        select: {
          name: true,
          domain: true,
          logoUrl: true
        },
        take: 10
      });

      remaining.forEach((company, index) => {
        console.log(`   ${index + 1}. ${company.name} (${company.domain})`);
      });
    }

    console.log("\n✅ Migration completed successfully!");
    console.log("   All available Clearbit logos have been downloaded and uploaded to R2 storage.");
    console.log("   Company logos are now served from Cloudflare R2 with proper CDN and caching.");

  } catch (error) {
    console.error("❌ Error:", error);
  } finally {
    await prisma.$disconnect();
  }
}

migrationSummary();
