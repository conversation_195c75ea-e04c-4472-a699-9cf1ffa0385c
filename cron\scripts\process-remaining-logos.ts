// Process remaining companies with failed Clearbit logos using existing logo processing system
import { PrismaClient } from "@prisma/client";
import { logger } from "../utils/logger";
import { uploadFile } from "../lib/storage/r2Storage";
import puppeteer from "puppeteer";

const prisma = new PrismaClient();

let globalBrowser: any = null;
let globalPage: any = null;

/**
 * Initialize browser for web scraping
 */
async function initializeBrowser() {
  if (!globalBrowser) {
    logger.info("🌐 Initializing browser for logo search...");
    globalBrowser = await puppeteer.launch({
      headless: true,
      args: [
        "--no-sandbox",
        "--disable-setuid-sandbox",
        "--disable-dev-shm-usage",
        "--disable-accelerated-2d-canvas",
        "--no-first-run",
        "--no-zygote",
        "--disable-gpu",
      ],
    });
    globalPage = await globalBrowser.newPage();
    await globalPage.setUserAgent(
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    );
  }
}

/**
 * Try to find logo using Bing Images search (existing logic from add-company-logos.ts)
 */
async function tryBingImagesSearchByName(companyName: string): Promise<string | null> {
  try {
    logger.info(`   🔍 Searching Bing Images for: ${companyName}`);

    // Ensure browser is initialized
    await initializeBrowser();

    // Search for company logo on Bing Images
    const searchQuery = encodeURIComponent(`${companyName} logo`);
    const bingUrl = `https://www.bing.com/images/search?q=${searchQuery}`;

    logger.info(`   🌐 Bing Images: ${bingUrl}`);

    await globalPage.goto(bingUrl, { waitUntil: "networkidle" });

    // Wait for images to load
    await globalPage.waitForTimeout(3000);

    // Get the first few image URLs
    const imageUrls = await globalPage.evaluate(() => {
      const images = document.querySelectorAll('img[src*="tse"]');
      return Array.from(images)
        .slice(0, 5)
        .map((img: any) => img.src)
        .filter((src: string) => src && src.startsWith("http"));
    });

    logger.info(`   📸 Found ${imageUrls.length} potential logo images`);

    // Try each image URL
    for (const imageUrl of imageUrls) {
      try {
        logger.info(`   🔄 Trying image: ${imageUrl.substring(0, 80)}...`);

        const imageResponse = await fetch(imageUrl);
        if (
          imageResponse.ok &&
          imageResponse.headers.get("content-type")?.startsWith("image/")
        ) {
          const buffer = Buffer.from(await imageResponse.arrayBuffer());
          const contentType =
            imageResponse.headers.get("content-type") || "image/png";

          // Generate a clean filename
          const cleanCompanyName = companyName
            .toLowerCase()
            .replace(/[^a-z0-9]/g, "-")
            .replace(/-+/g, "-")
            .replace(/^-|-$/g, "");

          const extension = contentType.includes("svg")
            ? "svg"
            : contentType.includes("jpeg")
              ? "jpg"
              : "png";
          const filename = `${cleanCompanyName}-bing-name-${Date.now()}.${extension}`;

          logger.info(`   📤 Uploading Bing logo to R2: ${filename}`);

          const uploadResult = await uploadFile(
            buffer,
            filename,
            contentType,
            "companyLogos"
          );

          if (uploadResult.success) {
            logger.info(`   ✅ Logo uploaded successfully: ${uploadResult.publicUrl}`);
            return uploadResult.publicUrl!;
          }
        }
      } catch (imageError) {
        logger.info(`   ⚠️ Failed to process image: ${imageError}`);
        continue;
      }
    }

    logger.info(`   ❌ No suitable logo found for ${companyName}`);
    return null;
  } catch (error) {
    logger.error(`   ❌ Error in Bing search for ${companyName}:`, error);
    return null;
  }
}

/**
 * Try Google Images search as alternative
 */
async function tryGoogleImagesSearch(companyName: string, domain: string): Promise<string | null> {
  try {
    logger.info(`   🔍 Searching Google Images for: ${companyName}`);

    // Ensure browser is initialized
    await initializeBrowser();

    // Search for company logo on Google Images
    const searchQuery = encodeURIComponent(`${companyName} ${domain} logo`);
    const googleUrl = `https://www.google.com/search?tbm=isch&q=${searchQuery}`;

    logger.info(`   🌐 Google Images: ${googleUrl}`);

    await globalPage.goto(googleUrl, { waitUntil: "networkidle" });

    // Wait for images to load
    await globalPage.waitForTimeout(3000);

    // Get image URLs from Google Images
    const imageUrls = await globalPage.evaluate(() => {
      const images = document.querySelectorAll('img[src*="http"]');
      return Array.from(images)
        .slice(0, 5)
        .map((img: any) => img.src)
        .filter((src: string) => 
          src && 
          src.startsWith("http") && 
          !src.includes("google.com") &&
          !src.includes("gstatic.com")
        );
    });

    logger.info(`   📸 Found ${imageUrls.length} potential logo images from Google`);

    // Try each image URL
    for (const imageUrl of imageUrls) {
      try {
        logger.info(`   🔄 Trying Google image: ${imageUrl.substring(0, 80)}...`);

        const imageResponse = await fetch(imageUrl);
        if (
          imageResponse.ok &&
          imageResponse.headers.get("content-type")?.startsWith("image/")
        ) {
          const buffer = Buffer.from(await imageResponse.arrayBuffer());
          const contentType =
            imageResponse.headers.get("content-type") || "image/png";

          // Generate a clean filename
          const cleanCompanyName = companyName
            .toLowerCase()
            .replace(/[^a-z0-9]/g, "-")
            .replace(/-+/g, "-")
            .replace(/^-|-$/g, "");

          const extension = contentType.includes("svg")
            ? "svg"
            : contentType.includes("jpeg")
              ? "jpg"
              : "png";
          const filename = `${cleanCompanyName}-google-${Date.now()}.${extension}`;

          logger.info(`   📤 Uploading Google logo to R2: ${filename}`);

          const uploadResult = await uploadFile(
            buffer,
            filename,
            contentType,
            "companyLogos"
          );

          if (uploadResult.success) {
            logger.info(`   ✅ Logo uploaded successfully: ${uploadResult.publicUrl}`);
            return uploadResult.publicUrl!;
          }
        }
      } catch (imageError) {
        logger.info(`   ⚠️ Failed to process Google image: ${imageError}`);
        continue;
      }
    }

    logger.info(`   ❌ No suitable logo found on Google for ${companyName}`);
    return null;
  } catch (error) {
    logger.error(`   ❌ Error in Google search for ${companyName}:`, error);
    return null;
  }
}

/**
 * Process remaining companies with failed Clearbit logos
 */
async function processRemainingLogos() {
  logger.info("🔄 Processing remaining companies with failed Clearbit logos...");

  try {
    // Find companies that still have Clearbit URLs (failed migration)
    const remainingCompanies = await prisma.company.findMany({
      where: {
        logoUrl: {
          contains: "clearbit.com"
        }
      },
      select: {
        id: true,
        name: true,
        domain: true,
        logoUrl: true,
        activeJobCount: true
      },
      orderBy: {
        activeJobCount: "desc"
      }
    });

    logger.info(`📊 Found ${remainingCompanies.length} companies with failed Clearbit logos`);

    if (remainingCompanies.length === 0) {
      logger.info("✅ No remaining companies to process!");
      return;
    }

    let processedCount = 0;
    let successCount = 0;

    for (const company of remainingCompanies) {
      try {
        logger.info(`🏢 Processing: ${company.name} (${company.activeJobCount || 0} jobs)`);
        logger.info(`   🔗 Failed Clearbit URL: ${company.logoUrl}`);

        let r2LogoUrl = null;

        // Method 1: Try Bing Images search
        r2LogoUrl = await tryBingImagesSearchByName(company.name);

        // Method 2: Try Google Images search if Bing failed
        if (!r2LogoUrl && company.domain) {
          logger.info(`   🔄 Bing failed, trying Google Images search...`);
          r2LogoUrl = await tryGoogleImagesSearch(company.name, company.domain);
        }

        if (r2LogoUrl) {
          // Update company with new R2 logo URL
          await prisma.company.update({
            where: { id: company.id },
            data: {
              logoUrl: r2LogoUrl
            }
          });

          logger.info(`   ✅ Updated company logo: ${r2LogoUrl}`);
          successCount++;
        } else {
          logger.info(`   ❌ No logo found for ${company.name} using any method`);
        }

        processedCount++;

        // Add delay between requests
        await new Promise(resolve => setTimeout(resolve, 2000));

      } catch (error) {
        logger.error(`❌ Error processing ${company.name}:`, error);
        processedCount++;
      }
    }

    logger.info("🎉 Remaining logo processing completed!");
    logger.info(`📊 Results:`);
    logger.info(`   ✅ Successfully processed: ${successCount}`);
    logger.info(`   ❌ Failed: ${processedCount - successCount}`);
    logger.info(`   📊 Total processed: ${processedCount}`);

  } catch (error) {
    logger.error("❌ Error during remaining logo processing:", error);
  } finally {
    // Clean up browser
    if (globalBrowser) {
      await globalBrowser.close();
    }
    await prisma.$disconnect();
  }
}

// Run the processing
processRemainingLogos().catch(console.error);
