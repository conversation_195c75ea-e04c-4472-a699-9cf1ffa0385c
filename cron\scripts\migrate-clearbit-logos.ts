// Migrate companies with Clearbit logo URLs to R2 storage
import { PrismaClient } from "@prisma/client";
import { logger } from "../utils/logger";
import { uploadFile } from "../lib/storage/r2Storage";

const prisma = new PrismaClient();

/**
 * Download image from Clearbit URL and upload to R2
 */
async function migrateClearbitLogoToR2(
  clearbitUrl: string,
  companyName: string,
  domain: string
): Promise<string | null> {
  try {
    logger.info(`   📥 Downloading Clearbit logo: ${clearbitUrl}`);

    const response = await fetch(clearbitUrl);
    if (!response.ok) {
      logger.error(
        `   ❌ Failed to download: ${response.status} ${response.statusText}`
      );
      return null;
    }

    const buffer = Buffer.from(await response.arrayBuffer());
    const contentType = response.headers.get("content-type") || "image/png";

    // Generate a clean filename
    const cleanCompanyName = companyName
      .toLowerCase()
      .replace(/[^a-z0-9]/g, "-")
      .replace(/-+/g, "-")
      .replace(/^-|-$/g, "");

    const extension = contentType.includes("svg") ? "svg" : "png";
    const filename = `${cleanCompanyName}-${domain.replace(/\./g, "-")}.${extension}`;

    logger.info(`   📤 Uploading to R2: ${filename}`);

    // Upload to R2 using our storage utility
    const uploadResult = await uploadFile(
      buffer,
      filename,
      contentType,
      "companyLogos"
    );

    if (uploadResult.success) {
      logger.info(
        `   ✅ Logo migrated successfully: ${uploadResult.publicUrl}`
      );
      return uploadResult.publicUrl!;
    } else {
      logger.error(`   ❌ Upload failed: ${uploadResult.error}`);
      return null;
    }
  } catch (error) {
    logger.error(`   ❌ Error migrating logo for ${domain}:`, error);
    return null;
  }
}

/**
 * Main function to migrate all Clearbit logos to R2
 */
async function migrateClearbitLogos() {
  logger.info("🔄 Starting Clearbit logo migration to R2...");

  try {
    // Find all companies with Clearbit logo URLs
    const companiesWithClearbitLogos = await prisma.company.findMany({
      where: {
        logoUrl: {
          contains: "clearbit.com",
        },
      },
      select: {
        id: true,
        name: true,
        domain: true,
        logoUrl: true,
        activeJobCount: true,
      },
      orderBy: {
        activeJobCount: "desc", // Process companies with most jobs first
      },
    });

    logger.info(
      `📊 Found ${companiesWithClearbitLogos.length} companies with Clearbit logos`
    );

    if (companiesWithClearbitLogos.length === 0) {
      logger.info(
        "✅ No companies with Clearbit logos found. Migration complete!"
      );
      return;
    }

    let migratedCount = 0;
    let failedCount = 0;

    for (const company of companiesWithClearbitLogos) {
      try {
        logger.info(
          `🏢 Processing: ${company.name} (${company.activeJobCount || 0} jobs)`
        );
        logger.info(`   🔗 Current Clearbit URL: ${company.logoUrl}`);

        if (!company.domain) {
          logger.warn(`   ⚠️ No domain for ${company.name}, skipping`);
          failedCount++;
          continue;
        }

        // Migrate the Clearbit logo to R2
        const r2LogoUrl = await migrateClearbitLogoToR2(
          company.logoUrl!,
          company.name,
          company.domain
        );

        if (r2LogoUrl) {
          // Update company with new R2 logo URL
          await prisma.company.update({
            where: { id: company.id },
            data: {
              logoUrl: r2LogoUrl,
            },
          });

          logger.info(`   ✅ Updated company logo: ${r2LogoUrl}`);
          migratedCount++;
        } else {
          logger.error(`   ❌ Failed to migrate logo for ${company.name}`);
          failedCount++;
        }

        // Add a small delay to avoid overwhelming the servers
        await new Promise((resolve) => setTimeout(resolve, 1000));
      } catch (error) {
        logger.error(`❌ Error processing ${company.name}:`, error);
        failedCount++;
      }
    }

    logger.info("🎉 Clearbit logo migration completed!");
    logger.info(`📊 Results:`);
    logger.info(`   ✅ Successfully migrated: ${migratedCount}`);
    logger.info(`   ❌ Failed: ${failedCount}`);
    logger.info(`   📊 Total processed: ${migratedCount + failedCount}`);
  } catch (error) {
    logger.error("❌ Error during Clearbit logo migration:", error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the migration
migrateClearbitLogos().catch(console.error);

export { migrateClearbitLogos };
