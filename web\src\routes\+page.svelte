<script lang="ts">
  import SEO from '$components/shared/SEO.svelte';
  import HeroSection from '../components/sections/HeroSection.svelte';
  import FeaturesSection from '../components/sections/FeaturesSection.svelte';
  import TestimonialsSection from '../components/sections/TestimonialsSection.svelte';
  import CompanySection from '../components/sections/CompanySection.svelte';
  import ServicesSection from '../components/sections/ServicesSection.svelte';
  import { JobCollections } from '$components/jobs';
  import CTASection from '../components/sections/CTASection.svelte';
  import OccupationsBackground from '$components/ui/OccupationsBackground.svelte';

  // Get the data from the server
  export let data;

  // Use data somewhere in your component
  // For example:
  $: userData = data.user;
</script>

<SEO
  title="Hirli - Automate Your Job Applications"
  description="Apply to hundreds of jobs with just one click. Our AI matches your resume to job listings, auto-fills applications, and tracks your progress."
  keywords="job application, automation, resume, job search, AI, career"
  url="https://hirli.com"
  image="/assets/og-image.jpg" />

<HeroSection />
<div class="bg-accent">
  <CompanySection />
  <!-- <OccupationsBackground /> -->
  <ServicesSection />
  <FeaturesSection />
  <TestimonialsSection />
  <JobCollections isAuthenticated={!!userData} />
  <CTASection />
</div>
