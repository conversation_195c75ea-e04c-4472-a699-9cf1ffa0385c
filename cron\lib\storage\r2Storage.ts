// cron/lib/storage/r2Storage.ts
// Cloudflare R2 storage service for file uploads and management

import {
  S3Client,
  PutObjectCommand,
  GetObjectCommand,
  DeleteObjectCommand,
  CopyObjectCommand,
  HeadBucketCommand,
  CreateBucketCommand,
  Put<PERSON>ucketCorsCommand,
  GetBucketCorsCommand,
} from "@aws-sdk/client-s3";
import { logger } from "../../utils/logger";
import path from "path";
import dotenv from "dotenv";

// Load environment variables
dotenv.config({ path: path.join(process.cwd(), ".env") });

// R2 Configuration - Using working endpoint with minimal configuration
const R2_CONFIG = {
  endpoint: "https://7efc1bf67e7d23f5683e06d0227c883f.r2.cloudflarestorage.com", // Fixed working endpoint
  region: "auto", // R2 uses 'auto' as region
  credentials: {
    accessKeyId:
      process.env.R2_ACCESS_KEY_ID || "c3a71f217fdf3a056efaefab3a17afc5",
    secretAccessKey:
      process.env.R2_SECRET_ACCESS_KEY ||
      "fa864b9366ee7aad37dd009db7a27cd0022d16813206cad6a02db955c0e79295",
  },
  forcePathStyle: true, // Required for R2 compatibility
};

// Initialize R2 client
const r2Client = new S3Client(R2_CONFIG);

// Fixed bucket names for different purposes
export enum BucketType {
  COMPANY = "company",
  RESUMES = "resumes",
  USER = "user",
  JOBS = "jobs",
  ANALYTICS = "analytics",
}

// Fixed bucket configurations - no environment overrides for consistency
const BUCKET_CONFIGS = {
  [BucketType.COMPANY]: {
    name: "hirli-company-logos",
    description: "Company logos, data, and assets",
  },
  [BucketType.RESUMES]: {
    name: "hirli-resume-files",
    description: "User resumes and documents",
  },
  [BucketType.USER]: {
    name: "hirli-user-images",
    description: "User profile pictures and personal files",
  },
  [BucketType.JOBS]: {
    name: "hirli-job-files",
    description: "Job-related files, screenshots, and assets",
  },
  [BucketType.ANALYTICS]: {
    name: "hirli-analytics",
    description: "Analytics reports, exports, and data",
  },
};

// File type configurations with bucket mapping
const FILE_CONFIGS = {
  companyLogos: {
    bucket: BucketType.COMPANY,
    folder: "logos",
    maxSize: 5 * 1024 * 1024, // 5MB
    allowedTypes: [
      "image/png",
      "image/jpeg",
      "image/jpg",
      "image/webp",
      "image/svg+xml",
    ],
  },
  companyData: {
    bucket: BucketType.COMPANY,
    folder: "data",
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ["application/json", "text/csv", "application/pdf"],
  },
  resumes: {
    bucket: BucketType.RESUMES,
    folder: "", // Store in root of bucket, same as company logos
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: [
      "application/pdf",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    ],
  },
  profilePictures: {
    bucket: BucketType.USER,
    folder: "avatars",
    maxSize: 2 * 1024 * 1024, // 2MB
    allowedTypes: ["image/png", "image/jpeg", "image/jpg", "image/webp"],
  },
  userDocuments: {
    bucket: BucketType.USER,
    folder: "documents",
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: [
      "application/pdf",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    ],
  },
  jobScreenshots: {
    bucket: BucketType.JOBS,
    folder: "screenshots",
    maxSize: 5 * 1024 * 1024, // 5MB
    allowedTypes: ["image/png", "image/jpeg", "image/jpg", "image/webp"],
  },
  jobAssets: {
    bucket: BucketType.JOBS,
    folder: "assets",
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: [
      "image/png",
      "image/jpeg",
      "image/jpg",
      "image/webp",
      "application/pdf",
      "text/html",
      "application/json",
    ],
  },
};

export type FileType = keyof typeof FILE_CONFIGS;

export interface UploadResult {
  success: boolean;
  fileKey?: string;
  publicUrl?: string;
  error?: string;
  fileSize?: number;
  contentType?: string;
  bucketName?: string;
}

export interface FileMetadata {
  originalName: string;
  contentType: string;
  size: number;
  uploadedAt: Date;
  folder: string;
}

/**
 * Get bucket name for a specific bucket type
 */
export function getBucketName(bucketType: BucketType): string {
  return BUCKET_CONFIGS[bucketType].name;
}

/**
 * Ensure bucket exists, create if it doesn't
 */
export async function ensureBucketExists(
  bucketType: BucketType
): Promise<boolean> {
  const bucketName = getBucketName(bucketType);

  try {
    // Check if bucket exists
    await r2Client.send(new HeadBucketCommand({ Bucket: bucketName }));
    logger.info(`✅ Bucket '${bucketName}' exists and is accessible`);
    return true;
  } catch (error: any) {
    if (error.name === "NotFound" || error.$metadata?.httpStatusCode === 404) {
      // Bucket doesn't exist, try to create it
      try {
        await r2Client.send(new CreateBucketCommand({ Bucket: bucketName }));
        logger.info(`✅ Bucket '${bucketName}' created successfully`);
        return true;
      } catch (createError: any) {
        if (
          createError.name === "BucketAlreadyExists" ||
          createError.name === "BucketAlreadyOwnedByYou"
        ) {
          logger.info(`✅ Bucket '${bucketName}' already exists`);
          return true;
        } else {
          logger.error(
            `❌ Failed to create bucket '${bucketName}':`,
            createError
          );
          return false;
        }
      }
    } else {
      logger.error(`❌ Error checking bucket '${bucketName}':`, error);
      return false;
    }
  }
}

/**
 * Initialize all buckets
 */
export async function initializeAllBuckets(): Promise<{
  success: boolean;
  results: Record<BucketType, boolean>;
}> {
  logger.info("🏗️ Initializing all R2 buckets...");

  const results: Record<BucketType, boolean> = {} as Record<
    BucketType,
    boolean
  >;
  let allSuccess = true;

  for (const bucketType of Object.values(BucketType)) {
    const success = await ensureBucketExists(bucketType);
    results[bucketType] = success;
    if (!success) allSuccess = false;
  }

  if (allSuccess) {
    logger.info("✅ All buckets initialized successfully");
  } else {
    logger.warn("⚠️ Some buckets failed to initialize");
  }

  return { success: allSuccess, results };
}

/**
 * Configure CORS policy for a bucket to allow web access
 */
export async function configureBucketCors(bucketType: BucketType): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    const bucketName = getBucketName(bucketType);

    logger.info(`🔧 Configuring CORS for bucket: ${bucketName}`);

    const corsConfiguration = {
      CORSRules: [
        {
          ID: "AllowWebAccess",
          AllowedHeaders: ["*"],
          AllowedMethods: ["GET", "HEAD"],
          AllowedOrigins: ["*"], // Allow all origins for public assets
          ExposeHeaders: ["ETag"],
          MaxAgeSeconds: 3600,
        },
      ],
    };

    const corsCommand = new PutBucketCorsCommand({
      Bucket: bucketName,
      CORSConfiguration: corsConfiguration,
    });

    await r2Client.send(corsCommand);

    logger.info(`✅ CORS configured successfully for bucket: ${bucketName}`);
    return { success: true };
  } catch (error: any) {
    logger.error(`❌ Failed to configure CORS for bucket:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown CORS error",
    };
  }
}

/**
 * Configure CORS for all buckets
 */
export async function configureAllBucketsCors(): Promise<{
  success: boolean;
  results: Record<BucketType, boolean>;
}> {
  logger.info("🔧 Configuring CORS for all R2 buckets...");

  const results: Record<BucketType, boolean> = {} as Record<
    BucketType,
    boolean
  >;
  let allSuccess = true;

  for (const bucketType of Object.values(BucketType)) {
    const result = await configureBucketCors(bucketType);
    results[bucketType] = result.success;
    if (!result.success) allSuccess = false;
  }

  if (allSuccess) {
    logger.info("✅ CORS configured successfully for all buckets");
  } else {
    logger.warn("⚠️ Some buckets failed CORS configuration");
  }

  return { success: allSuccess, results };
}

/**
 * Generate a unique file key for storage
 */
function generateFileKey(
  fileType: FileType,
  originalName: string,
  identifier?: string
): string {
  const extension = path.extname(originalName).toLowerCase();
  const config = FILE_CONFIGS[fileType];

  let uniqueFileName: string;

  if (fileType === "resumes" && identifier) {
    // For resumes, use profile-id or user-id as identifier
    // Format: resume-{identifier}.pdf (will be updated with resume-id after creation)
    uniqueFileName = `resume-${identifier}${extension}`;
  } else if (fileType === "companyLogos" && identifier) {
    // For company logos, use company-id
    const baseName = path
      .basename(originalName, extension)
      .replace(/[^a-zA-Z0-9-_]/g, "-")
      .toLowerCase()
      .substring(0, 30);
    uniqueFileName = `${baseName}-${identifier}${extension}`;
  } else {
    // For other file types, use original name with timestamp
    const baseName = path
      .basename(originalName, extension)
      .replace(/[^a-zA-Z0-9-_]/g, "-")
      .toLowerCase()
      .substring(0, 30);
    const timestamp = Date.now();
    uniqueFileName = `${baseName}-${timestamp}${extension}`;
  }

  // Handle folder structure based on file type
  if (config.folder) {
    // Files with folders: folder/filename
    return `${config.folder}/${uniqueFileName}`;
  } else {
    // Files without folders (like company logos and resumes): just filename in root
    return uniqueFileName;
  }
}

/**
 * Get public URL for a file - Use worker URL for consistent access
 */
export function getPublicUrl(fileKey: string, bucketName?: string): string {
  const customDomain = process.env.R2_CUSTOM_DOMAIN;
  if (customDomain) {
    return `https://${customDomain}/${fileKey}`;
  }

  // Use Cloudflare Worker URL for consistent access without hardcoded account IDs
  const workerUrl =
    process.env.R2_WORKER_URL ||
    "https://hirli-static-assets.christopher-eugene-rodriguez.workers.dev";

  // Return just the fileKey - worker will handle path prefixes
  return `${workerUrl}/${fileKey}`;
}

/**
 * Validate file before upload
 */
function validateFile(
  buffer: Buffer,
  contentType: string,
  fileType: FileType
): { valid: boolean; error?: string } {
  const config = FILE_CONFIGS[fileType];

  // Check file size
  if (buffer.length > config.maxSize) {
    return {
      valid: false,
      error: `File size ${buffer.length} exceeds maximum ${config.maxSize} bytes for ${fileType}`,
    };
  }

  // Check content type
  if (!config.allowedTypes.includes(contentType)) {
    return {
      valid: false,
      error: `Content type ${contentType} not allowed for ${fileType}. Allowed: ${config.allowedTypes.join(", ")}`,
    };
  }

  return { valid: true };
}

/**
 * Upload a file to R2 storage
 */
export async function uploadFile(
  buffer: Buffer,
  originalName: string,
  contentType: string,
  fileType: FileType,
  userId?: string
): Promise<UploadResult> {
  try {
    // Validate file
    const validation = validateFile(buffer, contentType, fileType);
    if (!validation.valid) {
      return {
        success: false,
        error: validation.error,
      };
    }

    const config = FILE_CONFIGS[fileType];
    const bucketName = getBucketName(config.bucket);

    // Skip bucket check for now - proceed directly to upload
    // Note: Bucket existence check requires additional permissions
    logger.info(`📤 Uploading to bucket: ${bucketName}`);

    // Generate unique file key
    const fileKey = generateFileKey(fileType, originalName, userId);

    // Prepare upload command
    const uploadCommand = new PutObjectCommand({
      Bucket: bucketName,
      Key: fileKey,
      Body: buffer,
      ContentType: contentType,
      Metadata: {
        originalName,
        uploadedAt: new Date().toISOString(),
        fileType,
        bucketType: config.bucket,
        ...(userId && { userId }),
      },
    });

    // Upload to R2
    await r2Client.send(uploadCommand);

    const publicUrl = getPublicUrl(fileKey, bucketName);

    logger.info(
      `✅ File uploaded successfully to ${bucketName}: ${fileKey} (${buffer.length} bytes)`
    );

    return {
      success: true,
      fileKey,
      publicUrl,
      fileSize: buffer.length,
      contentType,
      bucketName,
    };
  } catch (error) {
    logger.error(`❌ Failed to upload file ${originalName}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown upload error",
    };
  }
}

/**
 * Download a file from R2 storage
 */
export async function downloadFile(
  fileKey: string,
  bucketType?: BucketType
): Promise<{ success: boolean; buffer?: Buffer; error?: string }> {
  try {
    // If bucket type is provided, use it; otherwise use company bucket as default
    const bucketName = bucketType
      ? getBucketName(bucketType)
      : getBucketName(BucketType.COMPANY);

    const downloadCommand = new GetObjectCommand({
      Bucket: bucketName,
      Key: fileKey,
    });

    const response = await r2Client.send(downloadCommand);

    if (!response.Body) {
      return {
        success: false,
        error: "No file content received",
      };
    }

    // Convert stream to buffer
    const chunks: Uint8Array[] = [];
    const reader = response.Body.transformToWebStream().getReader();

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      chunks.push(value);
    }

    const buffer = Buffer.concat(chunks);

    logger.info(
      `✅ File downloaded successfully from ${bucketName}: ${fileKey} (${buffer.length} bytes)`
    );

    return {
      success: true,
      buffer,
    };
  } catch (error) {
    logger.error(`❌ Failed to download file ${fileKey}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown download error",
    };
  }
}

/**
 * Delete a file from R2 storage
 */
export async function deleteFile(
  fileKey: string,
  bucketType?: BucketType
): Promise<{ success: boolean; error?: string }> {
  try {
    // If bucket type is provided, use it; otherwise use company bucket as default
    const bucketName = bucketType
      ? getBucketName(bucketType)
      : getBucketName(BucketType.COMPANY);

    const deleteCommand = new DeleteObjectCommand({
      Bucket: bucketName,
      Key: fileKey,
    });

    await r2Client.send(deleteCommand);

    logger.info(`✅ File deleted successfully from ${bucketName}: ${fileKey}`);

    return { success: true };
  } catch (error) {
    logger.error(`❌ Failed to delete file ${fileKey}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown delete error",
    };
  }
}

/**
 * Update file key with resume ID after resume creation
 */
export async function updateResumeFileKey(
  oldFileKey: string,
  resumeId: string,
  bucketType: BucketType = BucketType.RESUMES
): Promise<{
  success: boolean;
  newFileKey?: string;
  newPublicUrl?: string;
  error?: string;
}> {
  try {
    const extension = path.extname(oldFileKey);
    const newFileKey = `resume-${resumeId}${extension}`;
    const bucketName = getBucketName(bucketType);

    logger.info(
      `🔄 Updating resume file key from ${oldFileKey} to ${newFileKey}`
    );

    // Copy object to new key
    const copyCommand = new CopyObjectCommand({
      Bucket: bucketName,
      CopySource: `${bucketName}/${oldFileKey}`,
      Key: newFileKey,
      MetadataDirective: "COPY",
    });

    await r2Client.send(copyCommand);

    // Delete old object
    const deleteCommand = new DeleteObjectCommand({
      Bucket: bucketName,
      Key: oldFileKey,
    });

    await r2Client.send(deleteCommand);

    const newPublicUrl = getPublicUrl(newFileKey, bucketName);

    logger.info(`✅ Resume file key updated successfully: ${newFileKey}`);

    return {
      success: true,
      newFileKey,
      newPublicUrl,
    };
  } catch (error) {
    logger.error(`❌ Failed to update resume file key:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Download image from URL and upload to R2
 */
export async function downloadAndUploadImage(
  imageUrl: string,
  fileName: string,
  fileType: FileType,
  userId?: string
): Promise<UploadResult> {
  try {
    logger.info(`📥 Downloading image from: ${imageUrl}`);

    // Download image
    const response = await fetch(imageUrl, {
      headers: {
        "User-Agent":
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
      },
    });

    if (!response.ok) {
      return {
        success: false,
        error: `Failed to download image: ${response.status} ${response.statusText}`,
      };
    }

    const buffer = Buffer.from(await response.arrayBuffer());
    const contentType = response.headers.get("content-type") || "image/png";

    // Upload to R2
    return await uploadFile(buffer, fileName, contentType, fileType, userId);
  } catch (error) {
    logger.error(`❌ Failed to download and upload image ${imageUrl}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}
