{"version": 3, "sources": ["../../svelte/src/version.js", "../../svelte/src/internal/disclose-version.js"], "sourcesContent": ["// generated during release, do not modify\n\n/**\n * The current version, as set in package.json.\n * @type {string}\n */\nexport const VERSION = '5.33.11';\nexport const PUBLIC_VERSION = '5';\n", "import { PUBLIC_VERSION } from '../version.js';\n\nif (typeof window !== 'undefined') {\n\t// @ts-expect-error\n\t((window.__svelte ??= {}).v ??= new Set()).add(PUBLIC_VERSION);\n}\n"], "mappings": ";AAOO,IAAM,iBAAiB;;;ACP9B;AAEA,IAAI,OAAO,WAAW,aAAa;AAElC,IAAE,YAAO,aAAP,OAAO,WAAa,CAAC,IAAG,MAAxB,GAAwB,IAAM,oBAAI,IAAI,IAAG,IAAI,cAAc;AAC9D;", "names": []}