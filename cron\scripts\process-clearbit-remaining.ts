// Process the remaining 4 companies with Clearbit logos using existing logo processing system
import { PrismaClient } from "@prisma/client";
import { logger } from "../utils/logger";
import { uploadFile } from "../lib/storage/r2Storage";

const prisma = new PrismaClient();

// Global browser instance to reuse across searches (same as add-company-logos.ts)
let globalBrowser: any = null;
let globalPage: any = null;

/**
 * Initialize browser once for all logo searches (same as add-company-logos.ts)
 */
async function initializeBrowser() {
  if (!globalBrowser) {
    const { chromium } = await import("playwright");

    logger.info("🌐 Launching browser for logo searches...");
    globalBrowser = await chromium.launch({
      headless: false, // Turn off headless mode as per user preference when automated detection fails
      args: ["--no-sandbox", "--disable-setuid-sandbox"],
    });

    const context = await globalBrowser.newContext({
      userAgent:
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    });

    globalPage = await context.newPage();
  }
}

/**
 * Close browser when done with all searches
 */
async function closeBrowser() {
  if (globalBrowser) {
    await globalBrowser.close();
    globalBrowser = null;
    globalPage = null;
  }
}

/**
 * Try to find logo using Bing Images search (existing logic from add-company-logos.ts)
 */
async function tryBingImagesSearchByName(
  companyName: string
): Promise<string | null> {
  try {
    logger.info(`   🔍 Searching Bing Images for: ${companyName}`);

    // Ensure browser is initialized
    await initializeBrowser();

    // Search for company logo on Bing Images
    const searchQuery = encodeURIComponent(`"${companyName}" logo company`);
    const bingUrl = `https://www.bing.com/images/search?q=${searchQuery}`;

    logger.info(`   🌐 Bing Images: ${bingUrl}`);

    await globalPage.goto(bingUrl, { waitUntil: "networkidle" });

    // Wait for images to load
    await globalPage.waitForTimeout(3000);

    // Extract image URLs from Bing Images results (same as add-company-logos.ts)
    const imageUrls = await globalPage.evaluate(() => {
      // Bing Images uses specific selectors for the actual image results
      const imageElements = Array.from(document.querySelectorAll(".iusc"));
      const urls: string[] = [];

      imageElements.forEach((element: any) => {
        try {
          const dataAttr = element.getAttribute("m");
          if (dataAttr) {
            const data = JSON.parse(dataAttr);
            if (data.murl) {
              urls.push(data.murl);
            }
          }
        } catch (e) {
          // Skip invalid elements
        }
      });

      return urls.slice(0, 3); // Get first 3 images
    });

    if (imageUrls.length === 0) {
      logger.info(`   ❌ No images found in Bing Images results`);
      return null;
    }

    logger.info(`   🎯 Found ${imageUrls.length} potential logo images`);

    // Try the image URLs
    for (let i = 0; i < imageUrls.length; i++) {
      const imageUrl = imageUrls[i];

      try {
        logger.info(`   📥 Trying image ${i + 1}: ${imageUrl}`);

        const imageResponse = await fetch(imageUrl, {
          headers: {
            "User-Agent":
              "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            Referer: "https://www.google.com/",
          },
          timeout: 8000, // 8 second timeout
        });
        if (
          imageResponse.ok &&
          imageResponse.headers.get("content-type")?.startsWith("image/")
        ) {
          const buffer = Buffer.from(await imageResponse.arrayBuffer());
          const contentType =
            imageResponse.headers.get("content-type") || "image/png";

          // Generate a clean filename
          const cleanCompanyName = companyName
            .toLowerCase()
            .replace(/[^a-z0-9]/g, "-")
            .replace(/-+/g, "-")
            .replace(/^-|-$/g, "");

          const extension = contentType.includes("svg")
            ? "svg"
            : contentType.includes("jpeg")
              ? "jpg"
              : "png";
          const filename = `${cleanCompanyName}-bing-name-${Date.now()}.${extension}`;

          logger.info(`   📤 Uploading Bing logo to R2: ${filename}`);

          const uploadResult = await uploadFile(
            buffer,
            filename,
            contentType,
            "companyLogos"
          );

          if (uploadResult.success) {
            logger.info(
              `   ✅ Bing logo uploaded successfully: ${uploadResult.publicUrl}`
            );
            return uploadResult.publicUrl!;
          } else {
            logger.error(`   ❌ Upload failed: ${uploadResult.error}`);
          }
        }
      } catch (imageError) {
        logger.info(`   ⚠️ Failed to download image ${i + 1}: ${imageError}`);
        continue;
      }
    }

    logger.info(`   ❌ No valid logos found via Bing Images`);
    return null;
  } catch (error) {
    logger.error(
      `   ❌ Error searching Bing Images for ${companyName}:`,
      error
    );
    return null;
  }
}

/**
 * Try Google Images search as alternative
 */
async function tryGoogleImagesSearch(
  companyName: string,
  domain: string
): Promise<string | null> {
  try {
    logger.info(`   🔍 Searching Google Images for: ${companyName}`);

    // Ensure browser is initialized
    await initializeBrowser();

    // Search for company logo on Google Images
    const searchQuery = encodeURIComponent(`"${companyName}" ${domain} logo`);
    const googleUrl = `https://www.google.com/search?tbm=isch&q=${searchQuery}`;

    logger.info(`   🌐 Google Images: ${googleUrl}`);

    await globalPage.goto(googleUrl, { waitUntil: "networkidle" });

    // Wait for images to load
    await globalPage.waitForTimeout(3000);

    // Get image URLs from Google Images
    const imageUrls = await globalPage.evaluate(() => {
      const images = document.querySelectorAll('img[src*="http"]');
      return Array.from(images)
        .slice(0, 3)
        .map((img: any) => img.src)
        .filter(
          (src: string) =>
            src &&
            src.startsWith("http") &&
            !src.includes("google.com") &&
            !src.includes("gstatic.com")
        );
    });

    logger.info(
      `   📸 Found ${imageUrls.length} potential logo images from Google`
    );

    // Try each image URL
    for (const imageUrl of imageUrls) {
      try {
        logger.info(
          `   🔄 Trying Google image ${imageUrls.indexOf(imageUrl) + 1}: ${imageUrl.substring(0, 80)}...`
        );

        const imageResponse = await fetch(imageUrl);
        if (
          imageResponse.ok &&
          imageResponse.headers.get("content-type")?.startsWith("image/")
        ) {
          const buffer = Buffer.from(await imageResponse.arrayBuffer());
          const contentType =
            imageResponse.headers.get("content-type") || "image/png";

          // Generate a clean filename
          const cleanCompanyName = companyName
            .toLowerCase()
            .replace(/[^a-z0-9]/g, "-")
            .replace(/-+/g, "-")
            .replace(/^-|-$/g, "");

          const extension = contentType.includes("svg")
            ? "svg"
            : contentType.includes("jpeg")
              ? "jpg"
              : "png";
          const filename = `${cleanCompanyName}-google-${Date.now()}.${extension}`;

          logger.info(`   📤 Uploading Google logo to R2: ${filename}`);

          const uploadResult = await uploadFile(
            buffer,
            filename,
            contentType,
            "companyLogos"
          );

          if (uploadResult.success) {
            logger.info(
              `   ✅ Logo uploaded successfully: ${uploadResult.publicUrl}`
            );
            return uploadResult.publicUrl!;
          }
        }
      } catch (imageError) {
        logger.info(`   ⚠️ Failed to process Google image: ${imageError}`);
        continue;
      }
    }

    logger.info(`   ❌ No suitable logo found on Google for ${companyName}`);
    return null;
  } catch (error) {
    logger.error(`   ❌ Error in Google search for ${companyName}:`, error);
    return null;
  }
}

/**
 * Process remaining companies with Clearbit logos
 */
async function processRemainingClearbitLogos() {
  logger.info(
    "🔄 Processing remaining companies with Clearbit logos using existing logo processing system..."
  );

  try {
    // Find companies that still have Clearbit URLs
    const remainingCompanies = await prisma.company.findMany({
      where: {
        logoUrl: {
          contains: "clearbit.com",
        },
      },
      select: {
        id: true,
        name: true,
        domain: true,
        logoUrl: true,
        activeJobCount: true,
      },
      orderBy: {
        activeJobCount: "desc",
      },
    });

    logger.info(
      `📊 Found ${remainingCompanies.length} companies with Clearbit logos to process`
    );

    if (remainingCompanies.length === 0) {
      logger.info("✅ No remaining companies to process!");
      return;
    }

    let processedCount = 0;
    let successCount = 0;

    for (const company of remainingCompanies) {
      try {
        logger.info(
          `🏢 Processing: ${company.name} (${company.activeJobCount || 0} jobs)`
        );
        logger.info(`   🔗 Current Clearbit URL: ${company.logoUrl}`);
        logger.info(`   🌐 Domain: ${company.domain || "No domain"}`);

        let r2LogoUrl = null;

        // Method 1: Try Bing Images search
        r2LogoUrl = await tryBingImagesSearchByName(company.name);

        // Method 2: Try Google Images search if Bing failed
        if (!r2LogoUrl && company.domain) {
          logger.info(`   🔄 Bing failed, trying Google Images search...`);
          r2LogoUrl = await tryGoogleImagesSearch(company.name, company.domain);
        }

        if (r2LogoUrl) {
          // Update company with new R2 logo URL
          await prisma.company.update({
            where: { id: company.id },
            data: {
              logoUrl: r2LogoUrl,
            },
          });

          logger.info(`   ✅ Updated company logo: ${r2LogoUrl}`);
          successCount++;
        } else {
          logger.info(
            `   ❌ No logo found for ${company.name} using any method`
          );
        }

        processedCount++;

        // Add delay between requests
        await new Promise((resolve) => setTimeout(resolve, 2000));
      } catch (error) {
        logger.error(`❌ Error processing ${company.name}:`, error);
        processedCount++;
      }
    }

    logger.info("🎉 Clearbit logo processing completed!");
    logger.info(`📊 Results:`);
    logger.info(`   ✅ Successfully processed: ${successCount}`);
    logger.info(`   ❌ Failed: ${processedCount - successCount}`);
    logger.info(`   📊 Total processed: ${processedCount}`);
  } catch (error) {
    logger.error("❌ Error during Clearbit logo processing:", error);
  } finally {
    // Clean up browser
    await closeBrowser();
    await prisma.$disconnect();
  }
}

// Run the processing
processRemainingClearbitLogos().catch(console.error);
