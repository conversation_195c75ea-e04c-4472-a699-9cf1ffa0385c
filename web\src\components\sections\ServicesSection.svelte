<script lang="ts">
  import {
    Target,
    Clock,
    BarChart4,
    Shield,
    Globe,
    CheckCircle,
    FileText,
    PenTool,
    Award,
    ArrowRight,
    MessageSquare,
    Sparkles,
  } from 'lucide-svelte';

  // Features object with improved wording
  const features = {
    // Automated Apply features
    automatedApply: {
      title: 'Effortless Job Applications',
      description:
        'Submit applications to hundreds of positions with our streamlined one-click system.',
      secondary: [
        {
          icon: Clock,
          title: 'Reclaim Your Time',
          description:
            'Save hours daily by automating repetitive tasks and focus on interview preparation.',
        },
        {
          icon: BarChart4,
          title: 'Performance Insights',
          description:
            'Gain valuable analytics to optimize your application strategy and improve results.',
        },
        {
          icon: Shield,
          title: 'Resume Enhancement',
          description:
            'Receive tailored suggestions to strengthen your resume for specific opportunities.',
        },
        {
          icon: Globe,
          title: 'Universal Platform Support',
          description:
            'Seamlessly works with LinkedIn, Indeed, Glassdoor, ZipRecruiter, and many more.',
        },
      ],
    },

    // Job Tracker features
    jobTracker: {
      title: 'Comprehensive Application Tracking',
      description: 'Monitor all your job applications in one intuitive, centralized dashboard.',
      secondary: [
        {
          icon: CheckCircle,
          title: 'Real-time Status Updates',
          description: 'Track your progress through each stage of the hiring process with clarity.',
        },
        {
          icon: Clock,
          title: 'Interview Management',
          description:
            'Organize and prepare for upcoming interviews with smart scheduling and reminders.',
        },
        {
          icon: BarChart4,
          title: 'Strategic Analytics',
          description:
            'Visualize your job search journey with detailed metrics and actionable insights.',
        },
        {
          icon: Shield,
          title: 'Enterprise-grade Security',
          description:
            'Rest assured your career data is protected with advanced encryption and privacy controls.',
        },
      ],
    },

    // Resume Builder features
    resumeBuilder: {
      title: 'Professional Resume Creator',
      description: 'Craft standout resumes that capture attention with our intuitive builder.',
      main: [
        {
          icon: FileText,
          title: 'Expert-designed Templates',
          description:
            'Choose from dozens of ATS-optimized templates crafted by hiring professionals.',
        },
        {
          icon: PenTool,
          title: 'Intuitive Customization',
          description: 'Personalize every aspect of your resume with our user-friendly editor.',
        },
      ],
      secondary: [
        {
          icon: Target,
          title: 'ATS-Friendly Formatting',
          description:
            'Ensure your resume successfully navigates through automated screening systems.',
        },
        {
          icon: Award,
          title: 'Strategic Skills Showcase',
          description:
            'Automatically highlight relevant qualifications based on target job descriptions.',
        },
        {
          icon: Globe,
          title: 'Versatile Export Options',
          description:
            'Download your polished resume in PDF, DOCX, or plain text formats as needed.',
        },
        {
          icon: Shield,
          title: 'Multiple Resume Versions',
          description:
            'Create and manage specialized resumes tailored for different career opportunities.',
        },
      ],
    },

    // Co-Pilot features
    coPilot: {
      title: 'AI Career Co-Pilot',
      description: 'Navigate your career journey with AI-powered guidance every step of the way.',
      secondary: [
        {
          icon: MessageSquare,
          title: 'AI Interview Coach',
          description:
            'Practice with realistic mock interviews tailored to your industry with instant feedback.',
        },
        {
          icon: Sparkles,
          title: 'Personalized Insights',
          description: 'Receive custom career advice based on your skills, experience, and goals.',
        },
        {
          icon: Target,
          title: 'Job Match Analysis',
          description:
            'Get AI-powered compatibility scores for job listings based on your profile.',
        },
        {
          icon: Shield,
          title: 'Career Strategy Planning',
          description:
            'Develop a strategic roadmap to achieve your long-term professional objectives.',
        },
      ],
    },
  };
</script>

<section id="services" class="relative mx-auto flex max-w-[90rem] flex-col gap-8">
  <!-- Automated Apply Section - Text Left, Image Right -->
  <div class="overflow-hidden rounded-3xl bg-white">
    <div class="grid grid-cols-1 lg:grid-cols-2">
      <!-- Text Content -->
      <div class="flex h-full flex-col justify-between space-y-6 p-[5rem]">
        <div class="flex flex-col text-left">
          <!-- Subheader -->
          <p class="text-sm font-medium uppercase tracking-wide text-blue-600">AUTOMATION</p>

          <!-- Header -->
          <h2 class="text-4xl font-light text-gray-900 md:text-5xl ">
            {features.automatedApply.title}
          </h2>

          <!-- Description -->
          <p class="text-lg leading-relaxed text-gray-600">
            {features.automatedApply.description}
          </p>

          <!-- CTA Button -->
          <a
            href="/auto-apply"
            class="inline-flex items-center rounded-lg bg-blue-600 px-6 py-3 text-white transition-colors hover:bg-blue-700">
            Learn More <ArrowRight class="ml-2 h-4 w-4" />
          </a>
        </div>
        <!-- 2-column features -->
        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
          {#each features.automatedApply.secondary.slice(0, 2) as feature}
            <div class="flex items-start gap-3">
              <div class="flex-shrink-0 rounded-lg bg-blue-100 p-2">
                <svelte:component this={feature.icon} class="h-4 w-4 text-blue-600" />
              </div>
              <div>
                <h4 class="text-sm font-semibold text-gray-900">{feature.title}</h4>
                <p class="text-xs text-gray-600">{feature.description}</p>
              </div>
            </div>
          {/each}
        </div>
      </div>

      <!-- Image/Visual - No padding, overflow at end -->
      <div
        class="relative my-[4rem] flex h-[600px] items-center justify-center overflow-hidden bg-gradient-to-br from-blue-50 to-indigo-100">
        <div class="text-center">
          <div
            class="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-blue-600">
            <Target class="h-8 w-8 text-white" />
          </div>
          <p class="font-medium text-gray-700">Automated Applications</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Job Tracker Section - Image Left, Text Right -->
  <div class="overflow-hidden rounded-3xl bg-white">
    <div class="grid grid-cols-1 lg:grid-cols-2">
      <!-- Image/Visual - No padding, overflow at end -->
      <div
        class="relative my-[4rem] flex items-center justify-center overflow-hidden bg-gradient-to-br from-green-50 to-emerald-100 lg:order-1">
        <div class="text-center">
          <div
            class="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-600">
            <CheckCircle class="h-8 w-8 text-white" />
          </div>
          <p class="font-medium text-gray-700">Application Tracking</p>
        </div>
      </div>

      <!-- Text Content -->
      <div class="space-y-6 p-[5rem] lg:order-2">
        <!-- Subheader -->
        <p class="text-sm font-medium uppercase tracking-wide text-green-600">TRACKING</p>

        <!-- Header -->
        <h2 class="text-4xl font-light text-gray-900 md:text-5xl">{features.jobTracker.title}</h2>

        <!-- Description -->
        <p class="text-lg leading-relaxed text-gray-600">
          {features.jobTracker.description}
        </p>

        <!-- 2-column features -->
        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
          {#each features.jobTracker.secondary.slice(0, 2) as feature}
            <div class="flex items-start gap-3">
              <div class="flex-shrink-0 rounded-lg bg-green-100 p-2">
                <svelte:component this={feature.icon} class="h-4 w-4 text-green-600" />
              </div>
              <div>
                <h4 class="text-sm font-semibold text-gray-900">{feature.title}</h4>
                <p class="text-xs text-gray-600">{feature.description}</p>
              </div>
            </div>
          {/each}
        </div>

        <!-- CTA Button -->
        <a
          href="/job-tracker"
          class="inline-flex items-center rounded-lg bg-green-600 px-6 py-3 text-white transition-colors hover:bg-green-700">
          Learn More <ArrowRight class="ml-2 h-4 w-4" />
        </a>
      </div>
    </div>
  </div>

  <!-- Resume Builder Section - Text Left, Image Right -->
  <div class="overflow-hidden rounded-3xl bg-white">
    <div class="grid grid-cols-1 lg:grid-cols-2">
      <!-- Text Content -->
      <div class="space-y-6 p-[5rem]">
        <!-- Subheader -->
        <p class="text-sm font-medium uppercase tracking-wide text-purple-600">RESUME</p>

        <!-- Header -->
        <h2 class="text-4xl font-light text-gray-900 md:text-5xl">
          {features.resumeBuilder.title}
        </h2>

        <!-- Description -->
        <p class="text-lg leading-relaxed text-gray-600">
          {features.resumeBuilder.description}
        </p>

        <!-- 2-column features -->
        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
          {#each features.resumeBuilder.secondary.slice(0, 2) as feature}
            <div class="flex items-start gap-3">
              <div class="flex-shrink-0 rounded-lg bg-purple-100 p-2">
                <svelte:component this={feature.icon} class="h-4 w-4 text-purple-600" />
              </div>
              <div>
                <h4 class="text-sm font-semibold text-gray-900">{feature.title}</h4>
                <p class="text-xs text-gray-600">{feature.description}</p>
              </div>
            </div>
          {/each}
        </div>

        <!-- CTA Button -->
        <a
          href="/resume-builder"
          class="inline-flex items-center rounded-lg bg-purple-600 px-6 py-3 text-white transition-colors hover:bg-purple-700">
          Learn More <ArrowRight class="ml-2 h-4 w-4" />
        </a>
      </div>

      <!-- Image/Visual - No padding, overflow at end -->
      <div
        class="relative my-[4rem] flex items-center justify-center overflow-hidden bg-gradient-to-br from-purple-50 to-violet-100">
        <div class="text-center">
          <div
            class="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-purple-600">
            <FileText class="h-8 w-8 text-white" />
          </div>
          <p class="font-medium text-gray-700">Resume Builder</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Co-Pilot Section - Image Left, Text Right -->
  <div class="overflow-hidden rounded-3xl bg-white">
    <div class="grid grid-cols-1 lg:grid-cols-2">
      <!-- Image/Visual - No padding, overflow at end -->
      <div
        class="relative my-[4rem] flex items-center justify-center overflow-hidden bg-gradient-to-br from-orange-50 to-amber-100 lg:order-1">
        <div class="text-center">
          <div
            class="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-orange-600">
            <Sparkles class="h-8 w-8 text-white" />
          </div>
          <p class="font-medium text-gray-700">AI Co-Pilot</p>
        </div>
      </div>

      <!-- Text Content -->
      <div class="space-y-6 space-y-6 p-[5rem] lg:order-2">
        <!-- Subheader -->
        <p class="text-sm font-medium uppercase tracking-wide text-orange-600">AI ASSISTANT</p>

        <!-- Header -->
        <h2 class="text-4xl font-light text-gray-900 md:text-5xl">{features.coPilot.title}</h2>

        <!-- Description -->
        <p class="text-lg leading-relaxed text-gray-600">
          {features.coPilot.description}
        </p>

        <!-- 2-column features -->
        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
          {#each features.coPilot.secondary.slice(0, 2) as feature}
            <div class="flex items-start gap-3">
              <div class="flex-shrink-0 rounded-lg bg-orange-100 p-2">
                <svelte:component this={feature.icon} class="h-4 w-4 text-orange-600" />
              </div>
              <div>
                <h4 class="text-sm font-semibold text-gray-900">{feature.title}</h4>
                <p class="text-xs text-gray-600">{feature.description}</p>
              </div>
            </div>
          {/each}
        </div>

        <!-- CTA Button -->
        <a
          href="/co-pilot"
          class="inline-flex items-center rounded-lg bg-orange-600 px-6 py-3 text-white transition-colors hover:bg-orange-700">
          Learn More <ArrowRight class="ml-2 h-4 w-4" />
        </a>
      </div>
    </div>
  </div>
</section>
